{"kind": "collectionType", "collectionName": "features", "info": {"singularName": "feature", "pluralName": "features", "displayName": "Feature", "description": "Features displayed on the landing page"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "description": {"type": "text", "required": true}, "icon": {"type": "enumeration", "enum": ["zap", "shield", "users", "globe", "star", "check", "arrow-right"], "default": "zap", "required": true}, "iconColor": {"type": "enumeration", "enum": ["green", "blue", "purple", "teal", "yellow", "red", "gray"], "default": "green", "required": true}, "order": {"type": "integer", "default": 1}, "isActive": {"type": "boolean", "default": true}}}