{"name": "Strapi CMS", "shortDescription": "Integrate with Strapi CMS to manage content types and entries", "longDescription": "This MCP server integrates with Strapi CMS to provide access to content types and entries through the MCP protocol. It enables AI assistants to create, read, update, and delete content in your Strapi instance, with support for filtering, pagination, sorting, and media uploads.", "iconUrl": "https://raw.githubusercontent.com/l33tdawg/strapi-mcp/main/icon.svg", "categories": ["content-management", "cms", "database"], "tags": ["strapi", "headless-cms", "content-api"], "installInstructions": "See DEPLOYMENT.md for detailed installation instructions", "environmentVariables": [{"name": "STRAPI_URL", "description": "URL of your Strapi instance (defaults to http://localhost:1337)", "required": false}, {"name": "STRAPI_API_TOKEN", "description": "Your Strapi API token for authentication", "required": true}, {"name": "STRAPI_DEV_MODE", "description": "Set to 'true' to enable development mode features (defaults to false)", "required": false}], "examples": [{"title": "List Content Types", "description": "Get all available content types in your Strapi instance", "code": "use_mcp_tool(\n  server_name: \"strapi-mcp\",\n  tool_name: \"list_content_types\",\n  arguments: {}\n)"}, {"title": "Get Filtered Entries", "description": "Get entries with filtering, pagination, and sorting", "code": "use_mcp_tool(\n  server_name: \"strapi-mcp\",\n  tool_name: \"get_entries\",\n  arguments: {\n    \"contentType\": \"api::article.article\",\n    \"filters\": {\n      \"title\": {\n        \"$contains\": \"hello\"\n      }\n    },\n    \"pagination\": {\n      \"page\": 1,\n      \"pageSize\": 10\n    },\n    \"sort\": [\"createdAt:desc\"]\n  }\n)"}, {"title": "Create Entry", "description": "Create a new entry for a content type", "code": "use_mcp_tool(\n  server_name: \"strapi-mcp\",\n  tool_name: \"create_entry\",\n  arguments: {\n    \"contentType\": \"api::article.article\",\n    \"data\": {\n      \"title\": \"My New Article\",\n      \"content\": \"This is the content of my article.\",\n      \"publishedAt\": \"2023-01-01T00:00:00.000Z\"\n    }\n  }\n)"}, {"title": "Upload Media", "description": "Upload a media file to Strapi", "code": "use_mcp_tool(\n  server_name: \"strapi-mcp\",\n  tool_name: \"upload_media\",\n  arguments: {\n    \"fileData\": \"base64-encoded-data-here\",\n    \"fileName\": \"image.jpg\",\n    \"fileType\": \"image/jpeg\"\n  }\n)"}]}